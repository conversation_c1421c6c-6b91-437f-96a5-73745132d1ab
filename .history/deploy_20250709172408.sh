#!/bin/bash

# Nebula Auth Multi Deployment Script
# This script helps build the Docker image and deploy using Helm

set -e

# Configuration
DOCKER_REGISTRY=${DOCKER_REGISTRY:-"164104811622.dkr.ecr.ap-southeast-1.amazonaws.com"}
IMAGE_NAME=${IMAGE_NAME:-"nebula-auth-multi"}
IMAGE_TAG=${IMAGE_TAG:-"0.0.1-alpha"}
HELM_RELEASE_NAME=${HELM_RELEASE_NAME:-"nebula-auth"}
KUBERNETES_NAMESPACE=${KUBERNETES_NAMESPACE:-"default"}
HELM_CHART_PATH="./helm-chart/nebula-auth-multi"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build       Build Docker image"
    echo "  push        Push Docker image to registry"
    echo "  deploy      Deploy using Helm"
    echo "  upgrade     Upgrade existing Helm deployment"
    echo "  uninstall   Uninstall Helm deployment"
    echo "  all         Build, push, and deploy"
    echo "  help        Show this help message"
    echo ""
    echo "Options:"
    echo "  -r, --registry REGISTRY    Docker registry (default: $DOCKER_REGISTRY)"
    echo "  -t, --tag TAG             Image tag (default: $IMAGE_TAG)"
    echo "  -n, --namespace NAMESPACE Kubernetes namespace (default: $KUBERNETES_NAMESPACE)"
    echo "  -f, --values-file FILE    Custom values file for Helm"
    echo "  --dry-run                 Perform a dry run"
    echo ""
    echo "Environment Variables:"
    echo "  DOCKER_REGISTRY          Docker registry URL"
    echo "  IMAGE_TAG                Docker image tag"
    echo "  KUBERNETES_NAMESPACE     Kubernetes namespace"
    echo "  HELM_RELEASE_NAME        Helm release name"
    echo ""
    echo "Examples:"
    echo "  $0 build -t v1.0.0"
    echo "  $0 deploy -f values-production.yaml -n production"
    echo "  $0 all -r my-registry.com -t v1.0.0 -n production"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check if Helm is installed
    if ! command -v helm &> /dev/null; then
        log_error "Helm is not installed or not in PATH"
        exit 1
    fi
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Check if Helm chart exists
    if [ ! -d "$HELM_CHART_PATH" ]; then
        log_error "Helm chart not found at $HELM_CHART_PATH"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

build_image() {
    log_info "Building Docker image..."
    
    FULL_IMAGE_NAME="${DOCKER_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
    
    if [ ! -f "nebula-auth-multi.jar" ]; then
        log_error "nebula-auth-multi.jar not found. Please ensure the JAR file is in the current directory."
        exit 1
    fi
    
    docker build -t "$FULL_IMAGE_NAME" .
    
    log_success "Docker image built: $FULL_IMAGE_NAME"
}

push_image() {
    log_info "Pushing Docker image to registry..."
    
    FULL_IMAGE_NAME="${DOCKER_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
    
    docker push "$FULL_IMAGE_NAME"
    
    log_success "Docker image pushed: $FULL_IMAGE_NAME"
}

deploy_helm() {
    log_info "Deploying with Helm..."
    
    HELM_ARGS="--namespace $KUBERNETES_NAMESPACE --create-namespace"
    
    if [ ! -z "$VALUES_FILE" ]; then
        HELM_ARGS="$HELM_ARGS -f $VALUES_FILE"
    fi
    
    if [ "$DRY_RUN" = "true" ]; then
        HELM_ARGS="$HELM_ARGS --dry-run"
    fi
    
    # Set image repository and tag
    HELM_ARGS="$HELM_ARGS --set image.repository=${DOCKER_REGISTRY}/${IMAGE_NAME} --set image.tag=${IMAGE_TAG}"
    
    helm install "$HELM_RELEASE_NAME" "$HELM_CHART_PATH" $HELM_ARGS
    
    if [ "$DRY_RUN" != "true" ]; then
        log_success "Helm deployment completed: $HELM_RELEASE_NAME"
        
        # Show deployment status
        kubectl get pods -n "$KUBERNETES_NAMESPACE" -l "app.kubernetes.io/instance=$HELM_RELEASE_NAME"
    fi
}

upgrade_helm() {
    log_info "Upgrading Helm deployment..."
    
    HELM_ARGS="--namespace $KUBERNETES_NAMESPACE"
    
    if [ ! -z "$VALUES_FILE" ]; then
        HELM_ARGS="$HELM_ARGS -f $VALUES_FILE"
    fi
    
    if [ "$DRY_RUN" = "true" ]; then
        HELM_ARGS="$HELM_ARGS --dry-run"
    fi
    
    # Set image repository and tag
    HELM_ARGS="$HELM_ARGS --set image.repository=${DOCKER_REGISTRY}/${IMAGE_NAME} --set image.tag=${IMAGE_TAG}"
    
    helm upgrade "$HELM_RELEASE_NAME" "$HELM_CHART_PATH" $HELM_ARGS
    
    if [ "$DRY_RUN" != "true" ]; then
        log_success "Helm upgrade completed: $HELM_RELEASE_NAME"
        
        # Show deployment status
        kubectl get pods -n "$KUBERNETES_NAMESPACE" -l "app.kubernetes.io/instance=$HELM_RELEASE_NAME"
    fi
}

uninstall_helm() {
    log_info "Uninstalling Helm deployment..."
    
    helm uninstall "$HELM_RELEASE_NAME" --namespace "$KUBERNETES_NAMESPACE"
    
    log_success "Helm deployment uninstalled: $HELM_RELEASE_NAME"
}

# Parse command line arguments
COMMAND=""
VALUES_FILE=""
DRY_RUN="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        build|push|deploy|upgrade|uninstall|all|help)
            COMMAND="$1"
            shift
            ;;
        -r|--registry)
            DOCKER_REGISTRY="$2"
            shift 2
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -n|--namespace)
            KUBERNETES_NAMESPACE="$2"
            shift 2
            ;;
        -f|--values-file)
            VALUES_FILE="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN="true"
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Main execution
case "$COMMAND" in
    build)
        check_prerequisites
        build_image
        ;;
    push)
        check_prerequisites
        push_image
        ;;
    deploy)
        check_prerequisites
        deploy_helm
        ;;
    upgrade)
        check_prerequisites
        upgrade_helm
        ;;
    uninstall)
        check_prerequisites
        uninstall_helm
        ;;
    all)
        check_prerequisites
        build_image
        push_image
        deploy_helm
        ;;
    help|"")
        usage
        ;;
    *)
        log_error "Unknown command: $COMMAND"
        usage
        exit 1
        ;;
esac
