# Multi-stage build for Nebula Auth Multi Service
FROM --platform=linux/amd64 openjdk:11-jre-slim as base

# Install required packages
RUN apt-get update && apt-get install -y \
    curl \
    netcat \
    && rm -rf /var/lib/apt/lists/*

# Create application user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Create application directories
RUN mkdir -p /app/conf /app/lib /apps/logs && \
    chown -R appuser:appuser /app /apps/logs

# Set working directory
WORKDIR /app

# Copy application files
COPY nebula-auth-multi.jar /app/
COPY lib/ /app/lib/
COPY conf/ /app/conf/

# Create a startup script
RUN cat > /app/start.sh << 'EOF'
#!/bin/bash
set -e

# Default values
JAVA_OPTS=${JAVA_OPTS:-"-Dapp.name=auth-service -Dapp.home=/app"}
JVM_OPTS=${JVM_OPTS:-"-Xms2g -Xmx2g -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"}
SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-"container"}

# Create log directory if it doesn't exist
mkdir -p /apps/logs/auth-service

# Wait for database to be ready (if DB_HOST is set)
if [ ! -z "$DB_HOST" ]; then
    echo "Waiting for database at $DB_HOST:${DB_PORT:-3306}..."
    while ! nc -z $DB_HOST ${DB_PORT:-3306}; do
        sleep 1
    done
    echo "Database is ready!"
fi

# Wait for Redis to be ready (if REDIS_HOST is set)
if [ ! -z "$REDIS_HOST" ]; then
    echo "Waiting for Redis at $REDIS_HOST:${REDIS_PORT:-6379}..."
    while ! nc -z $REDIS_HOST ${REDIS_PORT:-6379}; do
        sleep 1
    done
    echo "Redis is ready!"
fi

# Start the application
echo "Starting Nebula Auth Multi Service..."
echo "JAVA_OPTS: $JAVA_OPTS"
echo "JVM_OPTS: $JVM_OPTS"
echo "SPRING_PROFILES_ACTIVE: $SPRING_PROFILES_ACTIVE"

exec java $JVM_OPTS $JAVA_OPTS -Dspring.profiles.active=$SPRING_PROFILES_ACTIVE -jar /app/nebula-auth-multi.jar
EOF

# Make startup script executable
RUN chmod +x /app/start.sh

# Change ownership to application user
RUN chown -R appuser:appuser /app /apps/logs

# Switch to application user
USER appuser

# Expose port
EXPOSE 8085

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8085/oms/uaa/actuator/health || exit 1

# Set entrypoint
ENTRYPOINT ["/app/start.sh"]

# Labels
LABEL maintainer="DCITS Team <<EMAIL>>" \
      version="1.0.0" \
      description="Nebula Auth Multi Service - Authentication and authorization service" \
      org.opencontainers.image.title="Nebula Auth Multi" \
      org.opencontainers.image.description="Authentication and authorization service for the Nebula platform" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.vendor="DCITS" \
      org.opencontainers.image.source="https://github.com/dcits/nebula-auth-multi"
