1. Get the application URL by running these commands:
{{- if .Values.ingress.enabled }}
{{- range $host := .Values.ingress.hosts }}
  {{- range .paths }}
  http{{ if $.Values.ingress.tls }}s{{ end }}://{{ $host.host }}{{ .path }}
  {{- end }}
{{- end }}
{{- else if contains "NodePort" .Values.service.type }}
  export NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ include "nebula-auth-multi.fullname" . }})
  export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
  echo http://$NODE_IP:$NODE_PORT{{ .Values.app.contextPath }}
{{- else if contains "LoadBalancer" .Values.service.type }}
     NOTE: It may take a few minutes for the LoadBalancer IP to be available.
           You can watch the status of by running 'kubectl get --namespace {{ .Release.Namespace }} svc -w {{ include "nebula-auth-multi.fullname" . }}'
  export SERVICE_IP=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ include "nebula-auth-multi.fullname" . }} --template "{{"{{ range (index .status.loadBalancer.ingress 0) }}{{.}}{{ end }}"}}")
  echo http://$SERVICE_IP:{{ .Values.service.port }}{{ .Values.app.contextPath }}
{{- else if contains "ClusterIP" .Values.service.type }}
  export POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "nebula-auth-multi.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -o jsonpath="{.items[0].metadata.name}")
  export CONTAINER_PORT=$(kubectl get pod --namespace {{ .Release.Namespace }} $POD_NAME -o jsonpath="{.spec.containers[0].ports[0].containerPort}")
  echo "Visit http://127.0.0.1:8080{{ .Values.app.contextPath }} to use your application"
  kubectl --namespace {{ .Release.Namespace }} port-forward $POD_NAME 8080:$CONTAINER_PORT
{{- end }}

2. Check the application health:
{{- if .Values.healthCheck.enabled }}
  kubectl --namespace {{ .Release.Namespace }} get pods -l "app.kubernetes.io/name={{ include "nebula-auth-multi.name" . }},app.kubernetes.io/instance={{ .Release.Name }}"
  
  # Check health endpoint
  export POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name={{ include "nebula-auth-multi.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -o jsonpath="{.items[0].metadata.name}")
  kubectl --namespace {{ .Release.Namespace }} port-forward $POD_NAME 8080:{{ .Values.service.targetPort }}
  # Then visit: http://localhost:8080{{ .Values.app.contextPath }}/actuator/health
{{- end }}

3. View application logs:
  kubectl --namespace {{ .Release.Namespace }} logs -f deployment/{{ include "nebula-auth-multi.fullname" . }}

4. Configuration:
  - Service Name: {{ .Values.app.name }}
  - Context Path: {{ .Values.app.contextPath }}
  - Profile: {{ .Values.app.profile }}
  - Galaxy Tenant: {{ .Values.app.galaxy.tenant }}
  - Galaxy Workspace: {{ .Values.app.galaxy.workspace }}
  - Galaxy Region: {{ .Values.app.galaxy.region }}
  - Galaxy Availability Zone: {{ .Values.app.galaxy.availabilityZone }}

{{- if .Values.monitoring.enabled }}
5. Monitoring:
{{- if .Values.monitoring.prometheus.enabled }}
  - Prometheus metrics: http://service-url{{ .Values.app.contextPath }}/actuator/prometheus
{{- end }}
  - Health check: http://service-url{{ .Values.app.contextPath }}/actuator/health
  - Info endpoint: http://service-url{{ .Values.app.contextPath }}/actuator/info
{{- end }}

{{- if not .Values.database.password }}
WARNING: Database password is empty. Please set database.password in your values.yaml or via --set database.password=<password>
{{- end }}

{{- if and .Values.redis.password (eq .Values.redis.password "") }}
WARNING: Redis password is empty. If your Redis instance requires authentication, please set redis.password in your values.yaml
{{- end }}
