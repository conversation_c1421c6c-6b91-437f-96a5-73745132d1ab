# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
*.md
docs/

# He<PERSON> charts
helm-chart/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Build artifacts (keep only the final jar)
target/
build/
*.class

# Logs
*.log
logs/

# Environment files
.env
.env.local
.env.*.local

# Temporary files
tmp/
temp/

# Node modules (if any)
node_modules/

# Maven/Gradle wrapper (if not needed)
.mvn/
gradle/

# Test files
src/test/

# CI/CD files
.github/
.gitlab-ci.yml
Jenkinsfile

# Docker files (avoid recursion)
Dockerfile*
docker-compose*.yml
