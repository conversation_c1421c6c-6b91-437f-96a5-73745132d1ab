0000=Success
1000=Server error, please contact the administrator!
1001=Missing request parameters
1002=Illegal request parameter
1003=Failed to access resource
1004=user not found
1005=password error
1006=Database operation failed
1007=Session has expired, please log in again
1008=No permissions
1009=Not logged in
1010=Contains subordinate departments and cannot be deleted
1011=Department contains users and cannot be deleted
1012=Failed to add role
1013=Failed to update role
1014=Failed to delete role
1015=Failed to add menu
1016=Failed to update menu
1017=Failed to delete menu
1018=The menu contains submenus and cannot be deleted
1019=username already exists.
1020=Failed to add user
1021=Failed to update user
1022=Failed to delete user
1023=Password error
1024=Modifying user password error
1025=Unable to reset administrator password
1026=Resetting user password error
1027=Logout failed
1028=Unable to delete oneself
1029=Failed to add data dict
1030=Failed to update data dict
1031=Failed to delete data dict
1032=Data dict does not exist
1033=User has been disabled
1034=It is prohibited to delete the administrator role!\uFF01
1035=Original password error
1036=Institution number already exists
1037=Corporation number already exists
1038=Corporation must be bound to top-level institutions
1039=Verification code error
1040=password error
1041=Unable to reset one's own password
1042=Please modify the initial password before logging in
1043=Please check the username
1044=menu id already exist
1045=The system is busy, please try again
1046=The working directory does not exist
1047=No working directory permission
1048=Cannot add duplicate network cards
1049=Setting multiple system disks is not allowed
1050=Cannot add duplicate IP addresses
1051=Cannot add duplicate disks
1055=There is a workspace under the tenant, delete all workspaces before executing the deletion operation
1054=There are applications under the tenant, delete all applications before executing the deletion operation
1053=Unable to delete system tenant



0400=Authorization request parameter error
0401=Unauthorized requests
9999=operation failed


2001=Request data does not exist
2002=Data already exists
2003=Exceeding quota limit
2004=Platform product does not exist
2005=The platform product is currently being uninstalled. Please uninstall successfully before reinstalling
2006=invalid data
2007=App Store not enabled, unable to install/uninstall tenant level platform products
2008=Tenant is being deleted and updates are not allowed
2009=Container cloud not integrated, unable to install/uninstall tenant level platform products
2010=Host cannot be used by multiple clusters or tenants simultaneously


2014=Name already exists
2015=Email already exists


