# Production values for nebula-auth-multi
# This is an example configuration for production deployment

replicaCount: 3

image:
  repository: your-registry/nebula-auth-multi
  pullPolicy: IfNotPresent
  tag: "v1.0.0"

imagePullSecrets:
  - name: registry-secret

serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/nebula-auth-role

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "8085"
  prometheus.io/path: "/oms/uaa/actuator/prometheus"

podSecurityContext:
  fsGroup: 1000
  runAsNonRoot: true
  runAsUser: 1000

securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: false
  runAsNonRoot: true
  runAsUser: 1000

service:
  type: ClusterIP
  port: 8085
  targetPort: 8085

ingress:
  enabled: true
  className: nginx
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - host: auth.yourdomain.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: auth-tls
      hosts:
        - auth.yourdomain.com

resources:
  limits:
    cpu: 4000m
    memory: 8Gi
  requests:
    cpu: 1000m
    memory: 4Gi

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

nodeSelector:
  node-type: application

tolerations:
  - key: "application"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - nebula-auth-multi
        topologyKey: kubernetes.io/hostname

# Application configuration
app:
  name: auth-service
  profile: prod
  contextPath: /oms/uaa
  
  # JVM Options for production
  jvm:
    xms: 4g
    xmx: 6g
    metaspaceSize: 256m
    maxMetaspaceSize: 1g
    xss: 512k
    gcOptions: "-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCDateStamps"
    heapDumpOptions: "-XX:+HeapDumpOnOutOfMemoryError"
    additionalOptions: "-XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap"

  # Galaxy platform configuration
  galaxy:
    deployMode: container
    tenantMode: multi
    domain: yourdomain.com
    tenant: production
    workspace: main
    region: us-west-2
    availabilityZone: us-west-2a
    
    # Service URLs
    nebula:
      url: http://nebula-core-service.nebula-system:8080
    app:
      url: http://aries-app-center-service.galaxy4:8085
    vmapp:
      url: http://aries-legacy-app-center-service.galaxy4:8087

  # Authentication configuration
  auth:
    locale: en-US
    tokenStore: redis
    tokenTimeout: 3600  # 1 hour for production

  # Login configuration
  login:
    errorTimes: 3  # More restrictive for production
    reloginTime: 15  # Longer lockout time

  # Logging configuration
  logging:
    level:
      root: WARN
      dcits: INFO
    path: /apps/logs

# Database configuration
database:
  type: mysql
  host: prod-mysql.yourdomain.com
  port: 3306
  name: auth_prod
  username: auth_user
  password: "your-secure-database-password"
  # Connection pool settings for production
  druid:
    initialSize: 10
    maxWait: 30000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: "SELECT 1 FROM DUAL"
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    connectionProperties: "druid.stat.mergeSql=true;druid.stat.slowSqlMillis=2000"
    useGlobalDataSourceStat: true

# Redis configuration
redis:
  host: prod-redis.yourdomain.com
  port: 6379
  password: "your-secure-redis-password"
  database: 0

# Nacos configuration
nacos:
  enabled: true
  serverAddr: http://nacos-service.nacos:8848
  discovery:
    enabled: true
    group: "PROD_GROUP"
    namespace: "production"
    clusterName: "PROD_CLUSTER"
  config:
    enabled: false

# Health checks
healthCheck:
  enabled: true
  livenessProbe:
    httpGet:
      path: /oms/uaa/actuator/health
      port: 8085
    initialDelaySeconds: 120
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 3
  readinessProbe:
    httpGet:
      path: /oms/uaa/actuator/health
      port: 8085
    initialDelaySeconds: 60
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3

# Monitoring and metrics
monitoring:
  enabled: true
  prometheus:
    enabled: true
  actuator:
    endpoints:
      web:
        exposure:
          include: "health,info,metrics,prometheus"

# Persistence for logs
persistence:
  enabled: true
  storageClass: "fast-ssd"
  accessMode: ReadWriteOnce
  size: 100Gi
  mountPath: /apps/logs

# Environment variables
env:
  - name: ENVIRONMENT
    value: "production"
  - name: LOG_LEVEL
    value: "INFO"

# Pod disruption budget
podDisruptionBudget:
  enabled: true
  minAvailable: 2

# Network policies
networkPolicy:
  enabled: true
  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
      ports:
        - protocol: TCP
          port: 8085
    - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
      ports:
        - protocol: TCP
          port: 8085
  egress:
    - to:
        - namespaceSelector:
            matchLabels:
              name: database
      ports:
        - protocol: TCP
          port: 3306
    - to:
        - namespaceSelector:
            matchLabels:
              name: redis
      ports:
        - protocol: TCP
          port: 6379
    - to: []
      ports:
        - protocol: TCP
          port: 53
        - protocol: UDP
          port: 53
