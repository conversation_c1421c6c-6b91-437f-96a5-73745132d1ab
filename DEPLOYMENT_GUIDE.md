# Nebula Auth Multi - Kubernetes Deployment Guide

This guide provides comprehensive instructions for deploying the Nebula Auth Multi service to Kubernetes using Helm charts.

## Overview

The Nebula Auth Multi service is a Spring Boot application that provides authentication and authorization services for the Nebula platform. This deployment package includes:

- Complete Helm chart with best practices
- Docker containerization setup
- Production-ready configuration examples
- Automated deployment scripts

## Project Structure

```
nebula-auth-multi/
├── helm-chart/
│   └── nebula-auth-multi/
│       ├── Chart.yaml                 # Helm chart metadata
│       ├── values.yaml               # Default configuration values
│       ├── values-production.yaml    # Production configuration example
│       ├── README.md                 # Helm chart documentation
│       └── templates/
│           ├── _helpers.tpl          # Template helpers
│           ├── configmap.yaml        # Application configuration
│           ├── deployment.yaml       # Kubernetes Deployment
│           ├── service.yaml          # Kubernetes Service
│           ├── serviceaccount.yaml   # Service Account
│           ├── secret.yaml           # Secrets for DB/Redis
│           ├── ingress.yaml          # Ingress configuration
│           ├── pvc.yaml              # Persistent Volume Claim
│           ├── hpa.yaml              # Horizontal Pod Autoscaler
│           ├── poddisruptionbudget.yaml # Pod Disruption Budget
│           ├── networkpolicy.yaml    # Network Policy
│           └── NOTES.txt             # Post-installation notes
├── Dockerfile                        # Container image definition
├── .dockerignore                     # Docker ignore file
├── deploy.sh                         # Deployment automation script
└── DEPLOYMENT_GUIDE.md              # This guide
```

## Prerequisites

Before deploying, ensure you have:

1. **Kubernetes cluster** (v1.19+)
2. **Helm** (v3.2.0+)
3. **kubectl** configured for your cluster
4. **Docker** (for building images)
5. **MySQL database** accessible from the cluster
6. **Redis instance** accessible from the cluster
7. (Optional) **Nacos service discovery**

## Quick Start

### 1. Build the Docker Image

```bash
# Build the image
./deploy.sh build -t v1.0.0

# Or manually:
docker build -t your-registry/nebula-auth-multi:v1.0.0 .
```

### 2. Push to Registry

```bash
# Push the image
./deploy.sh push -r your-registry -t v1.0.0

# Or manually:
docker push your-registry/nebula-auth-multi:v1.0.0
```

### 3. Deploy with Helm

```bash
# Basic deployment
./deploy.sh deploy -r your-registry -t v1.0.0 \
  --set database.password=your-db-password \
  --set redis.password=your-redis-password

# Or manually:
helm install nebula-auth ./helm-chart/nebula-auth-multi \
  --set image.repository=your-registry/nebula-auth-multi \
  --set image.tag=v1.0.0 \
  --set database.password=your-db-password \
  --set redis.password=your-redis-password
```

## Configuration

### Essential Configuration

The following values must be configured for your environment:

```yaml
# Image configuration
image:
  repository: your-registry/nebula-auth-multi
  tag: v1.0.0

# Database configuration
database:
  host: your-mysql-host
  password: your-secure-password

# Redis configuration
redis:
  host: your-redis-host
  password: your-redis-password

# Galaxy platform configuration
app:
  galaxy:
    domain: your-domain.com
    tenant: your-tenant
    workspace: your-workspace
```

### Production Configuration

For production deployments, use the provided `values-production.yaml` as a starting point:

```bash
# Copy and customize the production values
cp helm-chart/nebula-auth-multi/values-production.yaml my-production-values.yaml

# Edit the file with your specific configuration
vim my-production-values.yaml

# Deploy with production values
./deploy.sh deploy -f my-production-values.yaml -n production
```

## Security Considerations

The Helm chart implements several security best practices:

### 1. Non-root User Execution
- Containers run as non-root user (UID 1000)
- Security contexts prevent privilege escalation

### 2. Secret Management
- Database and Redis passwords stored in Kubernetes Secrets
- Secrets are base64 encoded and mounted as environment variables

### 3. Network Security
- Optional NetworkPolicy for traffic control
- Service-to-service communication restrictions

### 4. Resource Limits
- CPU and memory limits prevent resource exhaustion
- Configurable resource requests and limits

## Monitoring and Observability

### Health Checks
- Liveness probe: Ensures container is running
- Readiness probe: Ensures service is ready for traffic
- Both use Spring Boot Actuator health endpoint

### Metrics
- Prometheus metrics enabled by default
- Spring Boot Actuator endpoints exposed
- Custom application metrics available

### Logging
- Structured logging with configurable levels
- Persistent volume for log storage
- Log rotation and retention policies

## High Availability

### Horizontal Pod Autoscaler
```yaml
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
```

### Pod Disruption Budget
```yaml
podDisruptionBudget:
  enabled: true
  minAvailable: 2
```

### Anti-Affinity Rules
```yaml
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - nebula-auth-multi
        topologyKey: kubernetes.io/hostname
```

## Troubleshooting

### Common Issues

1. **Database Connection Failures**
   ```bash
   # Check database connectivity
   kubectl run -it --rm debug --image=mysql:8.0 --restart=Never -- \
     mysql -h your-mysql-host -u your-username -p
   ```

2. **Redis Connection Issues**
   ```bash
   # Check Redis connectivity
   kubectl run -it --rm debug --image=redis:6.2 --restart=Never -- \
     redis-cli -h your-redis-host -p 6379
   ```

3. **Pod Startup Issues**
   ```bash
   # Check pod logs
   kubectl logs -f deployment/nebula-auth-multi
   
   # Check pod events
   kubectl describe pod <pod-name>
   ```

### Debugging Commands

```bash
# Check deployment status
kubectl get deployments
kubectl get pods
kubectl get services

# View configuration
kubectl get configmap nebula-auth-multi-config -o yaml

# Check secrets (base64 encoded)
kubectl get secret nebula-auth-multi-db-secret -o yaml

# Port forward for local testing
kubectl port-forward svc/nebula-auth-multi 8080:8085

# Check health endpoint
curl http://localhost:8080/oms/uaa/actuator/health
```

## Upgrade Process

### Rolling Updates
```bash
# Update image tag
./deploy.sh upgrade -t v1.1.0

# Or with custom values
helm upgrade nebula-auth ./helm-chart/nebula-auth-multi \
  --set image.tag=v1.1.0 \
  -f my-production-values.yaml
```

### Rollback
```bash
# View release history
helm history nebula-auth

# Rollback to previous version
helm rollback nebula-auth 1
```

## Backup and Recovery

### Database Backup
Ensure your MySQL database has proper backup procedures in place.

### Configuration Backup
```bash
# Backup Helm values
helm get values nebula-auth > nebula-auth-backup-values.yaml

# Backup Kubernetes resources
kubectl get all -l app.kubernetes.io/instance=nebula-auth -o yaml > nebula-auth-backup.yaml
```

## Performance Tuning

### JVM Tuning
```yaml
app:
  jvm:
    xms: 4g
    xmx: 6g
    gcOptions: "-XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

### Database Connection Pool
```yaml
database:
  druid:
    initialSize: 10
    maxWait: 30000
    timeBetweenEvictionRunsMillis: 60000
```

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review application logs
3. Consult the Helm chart README
4. Contact the DCITS support team

## License

This deployment configuration is provided under the Apache License 2.0.
