<configuration scan="true" scanPeriod="5 seconds">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="jupiter-logback-platform.xml" />
    <springProperty scope="context" name="logbackLoglevel" source="logging.level.root"
                    defaultValue="info"/>
    <springProperty scope="context" name="applicationName" source="spring.application.name"/>
<!--    <property name="logPath" value="/apps/logs"/>-->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <!-- Console 输出设置 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <root level="${logbackLoglevel}">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="JUPITER_LOG_FILE"/>
    </root>

</configuration>