# Default values for nebula-auth-multi
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: nebula-auth-multi
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "latest"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  fsGroup: 1000
  runAsNonRoot: true
  runAsUser: 1000

securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: false
  runAsNonRoot: true
  runAsUser: 1000

service:
  type: ClusterIP
  port: 8085
  targetPort: 8085
  protocol: TCP

ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: auth.local
      paths:
        - path: /
          pathType: Prefix
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  limits:
    cpu: 2000m
    memory: 4Gi
  requests:
    cpu: 500m
    memory: 2Gi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

# Application configuration
app:
  name: auth-service
  profile: container
  contextPath: /oms/uaa
  
  # JVM Options
  jvm:
    xms: 2g
    xmx: 2g
    metaspaceSize: 128m
    maxMetaspaceSize: 512m
    xss: 256k
    gcOptions: "-XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCDateStamps"
    heapDumpOptions: "-XX:+HeapDumpOnOutOfMemoryError"
    additionalOptions: ""

  # Galaxy platform configuration
  galaxy:
    deployMode: container
    tenantMode: multi
    domain: nebula.local
    tenant: nebula
    workspace: system
    region: default
    availabilityZone: dc01
    logicUnitId: ""
    phyUnitId: ""
    
    # Service URLs
    nebula:
      url: http://nebula-core-service.nebula-system:8080
    app:
      url: http://aries-app-center-service.galaxy4:8085
    vmapp:
      url: http://aries-legacy-app-center-service.galaxy4:8087

  # Authentication configuration
  auth:
    locale: zh-CN
    tokenStore: redis
    tokenTimeout: 7200

  # Login configuration
  login:
    errorTimes: 5
    reloginTime: 10

  # Logging configuration
  logging:
    level:
      root: INFO
      dcits: DEBUG
    path: /apps/logs

# Database configuration
database:
  type: mysql
  host: mysql-serviceonebankmysql.cvis2yua4tq4.ap-southeast-1.rds.amazonaws.com:3306
  port: 3306
  name: auth
  username: AUTH
  password: "1q2w3e4R!@#$"
  # Connection pool settings
  druid:
    initialSize: 5
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: "SELECT 1 FROM DUAL"
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    connectionProperties: "druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000"
    useGlobalDataSourceStat: true

# Redis configuration
redis:
  host: **********
  port: 6379
  password: ""
  database: 0

# Nacos configuration (for service discovery)
nacos:
  enabled: true
  serverAddr: http://**********:8848
  discovery:
    enabled: true
    group: "DEFAULT_GROUP"
    namespace: "public"
    clusterName: "DEFAULT"
  config:
    enabled: false

# Health checks
healthCheck:
  enabled: true
  livenessProbe:
    httpGet:
      path: /oms/uaa/actuator/health
      port: 8085
    initialDelaySeconds: 60
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 3
  readinessProbe:
    httpGet:
      path: /oms/uaa/actuator/health
      port: 8085
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3

# Monitoring and metrics
monitoring:
  enabled: true
  prometheus:
    enabled: true
  actuator:
    endpoints:
      web:
        exposure:
          include: "*"

# Persistence for logs
persistence:
  enabled: true
  storageClass: ""
  accessMode: ReadWriteOnce
  size: 10Gi
  mountPath: /apps/logs

# Environment variables
env: []
  # - name: CUSTOM_VAR
  #   value: "custom_value"

# Additional volumes
volumes: []

# Additional volume mounts
volumeMounts: []

# Pod disruption budget
podDisruptionBudget:
  enabled: false
  minAvailable: 1
  # maxUnavailable: 1

# Network policies
networkPolicy:
  enabled: false
  ingress: []
  egress: []
