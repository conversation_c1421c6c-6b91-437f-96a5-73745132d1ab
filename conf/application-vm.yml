server:
  servlet:
    session:
      cookie:
        secure: false
spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    username: AUTH
    password: 1q2w3e4R!@#$
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************************************************************************************
  cloud:
    nacos:
      discovery:
        enabled: ${SPRING_CLOUD_NACOS_DISCOVERY_ENABLED:true}
        server-addr: http://**********:8848
        group: ${galaxy.tenant}-${galaxy.workspace}
        namespace: ${galaxy.tenant}_${galaxy.workspace}
        clusterName: ${galaxy.availability-zone}

  redis:
    password: 
    host: **********
    port: 6379
  flyway:
    enabled: false

mybatis:
  configuration:
    database-id: mysql

auth:
  token-store: redis
  token-timeout: 7200

# 登录配置
login:
  error-times: 5 #登录失败次数配置（5次后锁定10分钟）
  relogin-time: 10 #锁定后重试间隔 单位分钟
logging:
  level:
    root: INFO
    com.dcits: DEBUG
  file:
    path: /app/dcits/app-run/logs
platform:
  base-url: onebank-paas.dcits.sg:7080
galaxy:
  deployMode: vm
  tenant: nebula
  workspace: system
  region: default
  availability-zone: dc01
  domain: onebank-paas.dcits.sg:7080
  store:
    url: http://onebank-paas.dcits.sg:7080
  vmapp:
    url: http://onebank-paas.dcits.sg:7080
  nacos:
    url: http://onebank-paas.dcits.sg:7080
cipher:
  #过期时间/天
  expire-duration: 90
