export REGION='xian'
export AVAILABLE_ZONE='dc01'
export SPRING_ELASTIC<PERSON>ARCH_JEST_URIS='http://10.0.43.97:9200'
export SPRING_ELASTICSEARCH_JEST_USERNAME='root'
export SPRING_ELASTICSEARCH_JEST_PASSWORD='123456'
export SPRING_<PERSON>LA<PERSON>ICSEARCH_JEST_READ_TIMEOUT='20000'
export SPRING_ELASTICSEARCH_JEST_CONNECTION_TIMEOUT='20000'
export ARIES_LOG_DEL_ES_DATA_TASK='7'
export VMOPTIONS='-XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCDateStamps -Xloggc:_LOG_DIR_/gc.log -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=_LOG_DIR_/heapdump -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -Xms2048m -Xmx4096m -Xss512k'
export SPRING_CLOUD_NACOS_CONFIG_ENABLED='false'
export SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR='http://10.0.43.95:8848'
export SPRING_CLOUD_NACOS_DISCOVERY_ENABLED='true'
export SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR='http://10.0.43.95:8848'
export NEBULA_AUTH_SERVICE_BASE_URL='http://auth-service:8085'
export LOGGING_FILE_PATH='/app/dcits/app-run/logs'
export DEPLOY_MODE='vm'
export ES_BACKUP_HOME='/app/dcits/app-run/galaxy/elasticsearch/elasticsearch-7.6.2/es-backup/'
export ES_DOWNLOAD_HOME='/app/dcits/app-run/galaxy/'
export LOG_BACKUP='true'
export LOGLEVEL='info'
export LOGBACK_MAX_HISTORY='14'
export LOGBACK_MAX_FILE_SIZE='10MB'
export LOGBACK_TOTAL_SIZE_CAP='30GB'
