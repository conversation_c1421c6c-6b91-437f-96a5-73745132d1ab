#!/bin/bash --login
usage(){
  echo "
Usage:
  start.sh [-p profile] [-d debug] [-j jmx] [-h] [-c] [-t tenant] [-z dataCenter] [-w workspace] [-l logicUnitId] [-y phyUnitId] [-r logRootDir] [-m cluster] [-g region] [-a availableZone] [-e, extend]
  -p, --profile       spring.profiles.active
  -d, --debug         active debug, input port
  -j, --jmx           active jmx, input port
  -h, --help          display this help and exit
  -c, --console       the console starts
  -t, --tenant        app.galaxy.tenant
  -z, --dataCenter    app.galaxy.dataCenter
  -w, --workspace     app.galaxy.workspace
  -l, --logicUnitId   app.galaxy.logicUnitId
  -y, --phyUnitId     app.galaxy.phyUnitId
  -r, --logRootDir    log root dir
  -m, --model         standalone or cluster，Set up nacos startup
  -g, --region        app.galaxy.region
  -a, --availableZone app.galaxy.availableZone
  -e, --extend        Customize extension parameters
"
}
cd `dirname $0`
BIN_DIR=`pwd`
cd ..
BASE_DIR=`pwd`
#平台日志存放默认定义目录
LOG_ROOT_DIR=''
CONF_DIR=${BASE_DIR}/conf
BIN_DIR=${BASE_DIR}/bin
#参数定义
CONSOLE_START=false
SPRING_PROFILES=""
JAVA_DEBUG_OPTS=""
JAVA_JMX_OPTS=""
GALAXY_TENANT=""
GALAXY_DATA_CENTER=""
GALAXY_WORKSPACE=""
GALAXY_LOGIC_UNITID=""
GALAXY_PHY_UNITID=""
JAVA_JMX_OPTS=""
profile=""
#nacos-x启动模式
MODEL=""
GALAXY_REGION=""
GALAXY_AVAILABLEZONE=""
CUSTOM_EXTENSION=""

function main(){
 # 选项有:表示该选项需要选项值
  while getopts "p:d:j:h:ct:z:w:l:y:r:m:g:a:e:" arg
  do
    case ${arg} in
      p)
        #参数存在$OPTARG中
        profile="${OPTARG}"
        if [ ! -f "${CONF_DIR}/application-${profile}.yml" -a  ! -f "${CONF_DIR}/bootstrap-${profile}.yml" ]; then
          echo "WARNING: Profile ${profile} does not exist!"
        fi
        if [ -n "${profile}" ] ; then
          SPRING_PROFILES="-Dspring.profiles.active=${profile}"
        fi
        ;;
      d)
        debugport="${OPTARG}"
        JAVA_DEBUG_OPTS="-Xdebug -Xnoagent -Djava.compiler=NONE -Xrunjdwp:transport=dt_socket,address=${debugport},server=y,suspend=n"
        echo "Using debug port: ${debugport}"
        ;;
      j)
        jmxport="${OPTARG}"
        JAVA_JMX_OPTS="-Dcom.sun.management.jmxremote.port=${jmxport} -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false"
        echo "Using jmx port: ${jmxport}"
        ;;
      h)
        usage
        exit 0
        ;;
      c)
        CONSOLE_START=true
        echo "Start the application from the console."
        ;;
      t)
        GALAXY_TENANT="${OPTARG}"
        echo "Using GALAXY_TENANT: ${GALAXY_TENANT}"
        ;;
      z)
        GALAXY_DATA_CENTER="${OPTARG}"
        echo "Using GALAXY_DATA_CENTER: ${GALAXY_DATA_CENTER}"
        ;;
      w)
        GALAXY_WORKSPACE="${OPTARG}"
        echo "Using GALAXY_WORKSPACE: ${GALAXY_WORKSPACE}"
        ;;
      l)
        GALAXY_LOGIC_UNITID="${OPTARG}"
        echo "Using GALAXY_LOGIC_UNITID: ${GALAXY_LOGIC_UNITID}"
        ;;
      y)
        GALAXY_PHY_UNITID="${OPTARG}"
        echo "Using GALAXY_PHY_UNITID: ${GALAXY_PHY_UNITID}"
        ;;
      r)
        LOG_ROOT_DIR="${OPTARG}"
        echo "Using LOG_ROOT_DIR: ${LOG_ROOT_DIR}"
        ;;
      m)
        MODEL="${OPTARG}"
        echo "Using Nacos StartUp MODEL: ${MODEL}"
        ;;
      g)
        GALAXY_REGION="${OPTARG}"
        echo "Using GALAXY_REGION: ${GALAXY_REGION}"
        ;;
      a)
        GALAXY_AVAILABLEZONE="${OPTARG}"
        echo "Using GALAXY_AVAILABLEZONE: ${GALAXY_AVAILABLEZONE}"
        ;;
      e)
        CUSTOM_EXTENSION="${OPTARG}"
        echo "Using CUSTOM_EXTENSION: ${CUSTOM_EXTENSION}"
        ;;
      ?)
        usage
        exit 1
        ;;
    esac
  done
}

function parse_yaml() {
  local yaml_file=$1
  local prefix=$2
  local s
  local w
  local fs

  if  [ -f ${yaml_file} ]; then
    s='[[:space:]]*'
    w='[a-zA-Z0-9_]*'
    fs="$(echo @|tr @ '\034')"

    (
      sed -ne 's/--//g; s/\"/\\\"/g; s/\#.*//g; s/\s*$//g; s/\$.*//g' \
        -e  "s|^\($s\)\($w\)$s:$s\"\(.*\)\"$s\$|\1$fs\2$fs\3|p" \
        -e "s|^\($s\)\($w\)$s[:-]$s\(.*\)$s\$|\1$fs\2$fs\3|p" |
      awk -F"$fs" '{
        indent = length($1)/2;
        if (length($2) == 0) { conj[indent]="+";} else {conj[indent]="";}
        vname[indent] = $2;
        for (i in vname) {if (i > indent) {delete vname[i]}}
          if (length($3) > 0) {
            vn=""; for (i=0; i<indent; i++) {vn=(vn)(vname[i])("_")}
            printf("%s%s%s%s=(\"%s\")\n", "'"$prefix"'",vn, $2, conj[indent-1],$3);
          }
        }' |
      sed 's/_=/+=/g'
    ) < "${yaml_file}"
  fi
}

function getProperty {
 local prop_file=$1
 local prop_key=$2
 local prop_value

 if  [ -f ${prop_file} ]; then
   prop_value=`cat ${prop_file} | grep "^${prop_key}" | cut -d'=' -f2`
   echo ${prop_value}
 fi
}
main "$@"
if  [ -n "${profile}" ] ; then
  echo "Specified Profile: ${profile}"
else
  eval $(parse_yaml ${CONF_DIR}/bootstrap.yml "profile_") 2>/dev/null
  eval $(parse_yaml ${CONF_DIR}/application.yml "profile_") 2>/dev/null
  profile=$profile_spring_profiles_active
  if [[ ${profile} != "" ]]; then
    SPRING_PROFILES="-Dspring.profiles.active=${profile}"
  fi
fi
if [[ ${SPRING_PROFILES} != "" ]]; then
  echo "spring application profiles: ${SPRING_PROFILES}"
fi
eval $(parse_yaml ${CONF_DIR}/bootstrap.yml "application_") 2>/dev/null
eval $(parse_yaml ${CONF_DIR}/bootstrap-$profile.yml "application_") 2>/dev/null
eval $(parse_yaml ${CONF_DIR}/application.yml "application_") 2>/dev/null
eval $(parse_yaml ${CONF_DIR}/application-$profile.yml "application_") 2>/dev/null
SERVER_PORT=$application_server_port
SERVER_NAME=$application_servicename
# 如果应用未配置servicename，解析 application_spring_application_name
if  [[ -z "${SERVER_NAME}" ]] ; then
  SERVER_NAME=$application_spring_application_name
fi
#Nacos的特殊解析
if  [[ -z "${SERVER_PORT}" && -z "${SERVER_NAME}" ]] ; then
  SERVER_PORT=$(getProperty ${CONF_DIR}/application.properties "server.port")
  SERVER_NAME=$(getProperty ${CONF_DIR}/application.properties "servicename")
fi

echo "Server Port: ${SERVER_PORT}; Server Name: ${SERVER_NAME}"

files=$(ls ${BASE_DIR}/*.jar)
for filename in $files
do
 # 匹配以.jar结尾的文件
 if [[ ${filename} =~ \.jar$ ]]; then
  APPLICATION_JAR=${filename}
 fi
done
#如果主jar为空，则退出
if [[ -z ${APPLICATION_JAR} ]]; then
  echo "APPLICATION_JAR is empty,Please check the package structure!!!"
  exit;
fi
#检查程序是否在运行
pid=`ps -ef|grep ${BASE_DIR}/${APPLICATION_JAR}|grep -v grep|awk '{print $2}' `
#如果不存在执行启动
if [ ! -z "${pid}" ]; then
  echo "The application is running and cannot be started!"
  exit 1
fi
#检查待启动程序端口是否已被占用
PORT_COUNT=`netstat -nlt|grep $SERVER_PORT|wc -l`
if [[ $PORT_COUNT -gt 0 ]]; then
  echo "The application port $SERVER_PORT was already in use!"
  exit 1
fi

#基于用户-r后，创建日志目录
if [[ -z "${LOG_ROOT_DIR}" ]] ; then
  # -r 参数未指定日志目录时，读取yaml参数: logging.file.path
  LOG_ROOT_DIR=$application_logging_file_path
  if [[ -z "${LOG_ROOT_DIR}" ]] ; then
    # 未配置日志目录时，使用默认
    LOG_ROOT_DIR=/apps/logs
  fi
fi

if [ ! -d ${LOG_ROOT_DIR} ]; then
    mkdir -p ${LOG_ROOT_DIR}
fi

LOG_FILE="stdout.log"
LOG_DIR=${LOG_ROOT_DIR}/${SERVER_NAME}
if [ ! -d ${LOG_DIR} ]; then
  mkdir -p ${LOG_DIR}
fi
LOG_PATH="${LOG_DIR}/${LOG_FILE}"

# jvm options
#GC_OPTS="-XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCDateStamps -Xloggc:${LOG_DIR}/gc.log"
#HEAP_DUMP_OPTS="-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${LOG_DIR}/heapdump"
#JVM_OPTS="-XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -Xms128m -Xmx128m -Xss256k"
#JVM_OPTS="${JVM_OPTS} ${GC_OPTS} ${HEAP_DUMP_OPTS}"
#执行扩展shell可根据环境重置JVM_OPTS
vmoption_file=app.vmoptions
if  [ -n "${profile}" ] ; then
  vmoption_file=app-${profile}.vmoptions
fi
vmoption_file_dir=${BIN_DIR}/vmoptions/${vmoption_file}
if [ ! -f "${vmoption_file_dir}" ]; then
  echo "Not found vm optison in the ${vmoption_file_dir} file,use the default file app.vmoptions!"
  vmoption_file_dir=${BIN_DIR}/vmoptions/app.vmoptions
fi
echo "Using Vmoptions File Path : ${vmoption_file_dir}"
# 读取环境变量是否存在，gc日志增加POD名称
temp_log_dir=${LOG_DIR}
if [[ ! -z "${HOSTNAME}" ]]; then
  temp_log_dir=${temp_log_dir}/${HOSTNAME}
  if [ ! -d ${temp_log_dir} ]; then
    mkdir -p ${temp_log_dir}
  fi
fi
temp_log_dir=`echo ${temp_log_dir}|sed 's/\//%/g'`
JVM_OPTS=''
# 读取环境变量中VMOPTIONS；有值：以配置值为主，无值: 读取app-xxx.vmoptions文件
if [[ ! -z "${VMOPTIONS}" ]]; then
  JVM_OPTS=${VMOPTIONS}
  echo "Read env variable: ${JVM_OPTS}"
else
  JVM_OPTS=`sed -e "s/_LOG_DIR_/${temp_log_dir}/g; s/%/\//g; s/\#.*//g" ${vmoption_file_dir}`
  echo "Read vm file: ${JVM_OPTS}"
fi
JAVA_OPTS="-Dapp.name=${SERVER_NAME}"
JAVA_OPTS="${JAVA_OPTS} -Dapp.home=${BASE_DIR}"
if [[ ${SPRING_PROFILES} != "" ]]; then
  JAVA_OPTS="${JAVA_OPTS} ${SPRING_PROFILES}"
fi
if [[ ${GALAXY_TENANT} != "" ]]; then
  JAVA_OPTS="${JAVA_OPTS} -Dgalaxy.tenant=${GALAXY_TENANT}"
fi
if [[ ${GALAXY_DATA_CENTER} != "" ]]; then
  JAVA_OPTS="${JAVA_OPTS} -Dgalaxy.dataCenter=${GALAXY_DATA_CENTER}"
fi
if [[ ${GALAXY_WORKSPACE} != "" ]]; then
  JAVA_OPTS="${JAVA_OPTS} -Dgalaxy.workspace=${GALAXY_WORKSPACE}"
fi
if [[ ${GALAXY_LOGIC_UNITID} != "" ]]; then
  JAVA_OPTS="${JAVA_OPTS} -Dgalaxy.logicUnitId=${GALAXY_LOGIC_UNITID}"
fi
if [[ ${GALAXY_PHY_UNITID} != "" ]]; then
  JAVA_OPTS="${JAVA_OPTS} -Dgalaxy.phyUnitId=${GALAXY_PHY_UNITID}"
fi
if [[ ${GALAXY_REGION} != "" ]]; then
  JAVA_OPTS="${JAVA_OPTS} -Dgalaxy.region=${GALAXY_REGION}"
fi
if [[ ${GALAXY_AVAILABLEZONE} != "" ]]; then
  JAVA_OPTS="${JAVA_OPTS} -Dgalaxy.availablezone=${GALAXY_AVAILABLEZONE}"
fi
if [[ ${CUSTOM_EXTENSION} != "" ]]; then
  JAVA_OPTS="${JAVA_OPTS} ${CUSTOM_EXTENSION}"
fi
if [[ ${JAVA_DEBUG_OPTS} != "" ]]; then
  JAVA_OPTS="${JAVA_OPTS} ${JAVA_DEBUG_OPTS}"
fi
if [[ ${JAVA_JMX_OPTS} != "" ]]; then
  JAVA_OPTS="${JAVA_OPTS} ${JAVA_JMX_OPTS}"
fi
if [ ${CONSOLE_START} = true ]; then
  java ${JVM_OPTS} ${JAVA_OPTS} -jar ${APPLICATION_JAR} ${MODEL}
else
  nohup java ${JVM_OPTS} ${JAVA_OPTS} -jar ${APPLICATION_JAR} ${MODEL} > ${LOG_PATH} 2>&1 &

  PORT_DETECT_CMD=""
  if [ -x /usr/sbin/ss ]; then
    PORT_DETECT_CMD="/usr/sbin/ss -lnt | grep $SERVER_PORT | wc -l"
  elif [ -x /usr/bin/netstat ]; then
    PORT_DETECT_CMD="/usr/bin/netstat -lnt | grep $SERVER_PORT | wc -l"
  elif [ -x /usr/bin/lsof ]; then
    PORT_DETECT_CMD="/usr/bin/lsof -i :$SERVER_PORT | grep -v COMMAND | wc -l"
  fi

  sleep 2
  COUNT=`ps -f | grep java | grep ${BASE_DIR} | awk '{print $2}' | wc -l`
#  while true; do
#    COUNT=`ps -f | grep java | grep ${BASE_DIR} | awk '{print $2}' | wc -l`
#    if [ ! $COUNT -gt 0 ]; then
#      break
#    elif [[ $PORT_COUNT -gt 0 ]]; then
#      break
#    fi
#
#    COUNT=`eval ${PORT_DETECT_CMD}`
#    [ ${COUNT} -gt 0 ] && break
#
#    echo -e ".\c"
#    sleep 1
#  done

  if [ ${COUNT} -gt 0 ]; then
    echo "${SERVER_NAME} start ok!"
    PIDS=`ps -f | grep java | grep ${BASE_DIR} | awk '{print $2}'`
    echo "PID                   : ${PIDS}"
    echo "Using CLASSPATH       : ${CLASSPATH}"
    echo "Using JAVA_HONE       : ${JAVA_HOME}"
    echo "Using JAVA_VERSION    : `${JAVA_HOME}/bin/java -version 2>&1 |awk '{a[NR]=$x} END{for(i=1;i<=NR;i++){printf(a[i]" ")}}'|awk '{print $10,$11,$3,$12}'`"
    echo "Using APPLICATION_JAR : ${APPLICATION_JAR}"
    echo "Using JAVA_OPTS       : ${JAVA_OPTS}"
    echo "Using JVM_OPTS        : ${JVM_OPTS}"
    echo "Using DEPLOY_DIR      : ${BASE_DIR}"
    echo "Using LOGS_DIR        : ${LOG_DIR}"
    echo "Using CONF_DIR        : ${CONF_DIR}"
    echo "Nohup LOG_PATH        : ${LOG_PATH}"
  else
    echo "${SERVER_NAME} start failed!"
  fi
fi
