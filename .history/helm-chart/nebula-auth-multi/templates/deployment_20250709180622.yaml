apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "nebula-auth-multi.fullname" . }}
  labels:
    {{- include "nebula-auth-multi.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "nebula-auth-multi.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "nebula-auth-multi.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "nebula-auth-multi.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: {{ include "nebula-auth-multi.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.targetPort }}
              protocol: TCP
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: {{ .Values.app.profile | quote }}
            - name: JAVA_OPTS
              value: >-
                -Dapp.name={{ .Values.app.name }}
                -Dapp.home=/app
                -Dspring.profiles.active={{ .Values.app.profile }}
                -Dgalaxy.tenant={{ .Values.app.galaxy.tenant }}
                -Dgalaxy.dataCenter={{ .Values.app.galaxy.availabilityZone }}
                -Dgalaxy.workspace={{ .Values.app.galaxy.workspace }}
                -Dgalaxy.region={{ .Values.app.galaxy.region }}
                -Dgalaxy.availablezone={{ .Values.app.galaxy.availabilityZone }}
                {{- if .Values.app.galaxy.logicUnitId }}
                -Dgalaxy.logicUnitId={{ .Values.app.galaxy.logicUnitId }}
                {{- end }}
                {{- if .Values.app.galaxy.phyUnitId }}
                -Dgalaxy.phyUnitId={{ .Values.app.galaxy.phyUnitId }}
                {{- end }}
            - name: JVM_OPTS
              value: {{ include "nebula-auth-multi.jvmOptions" . | quote }}
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "nebula-auth-multi.databaseSecretName" . }}
                  key: password
            - name: REDIS_PASSWORD
              {{- if .Values.redis.password }}
              valueFrom:
                secretKeyRef:
                  name: {{ include "nebula-auth-multi.redisSecretName" . }}
                  key: password
              {{- else }}
              value: ""
              {{- end }}
            {{- if .Values.nacos.enabled }}
            - name: SPRING_CLOUD_NACOS_DISCOVERY_ENABLED
              value: {{ .Values.nacos.discovery.enabled | quote }}
            - name: SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR
              value: {{ .Values.nacos.serverAddr | quote }}
            - name: SPRING_CLOUD_NACOS_CONFIG_ENABLED
              value: {{ .Values.nacos.config.enabled | quote }}
            {{- end }}
            - name: LOGGING_FILE_PATH
              value: {{ .Values.app.logging.path | quote }}
            - name: DEPLOY_MODE
              value: {{ .Values.app.galaxy.deployMode | quote }}
            {{- range .Values.env }}
            - name: {{ .name }}
              value: {{ .value | quote }}
            {{- end }}
          {{- if .Values.healthCheck.enabled }}
          livenessProbe:
            {{- toYaml .Values.healthCheck.livenessProbe | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.healthCheck.readinessProbe | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: config-volume
              mountPath: /app/conf/application.yml
              subPath: application.yml
              readOnly: true
            {{- if .Values.persistence.enabled }}
            - name: logs-volume
              mountPath: {{ .Values.persistence.mountPath }}
            {{- end }}
            {{- with .Values.volumeMounts }}
            {{- toYaml . | nindent 12 }}
            {{- end }}
          command: ["java"]
          args:
            - "$(JVM_OPTS)"
            - "$(JAVA_OPTS)"
            - "-jar"
            - "/app/nebula-auth-multi.jar"
      volumes:
        - name: config-volume
          configMap:
            name: {{ include "nebula-auth-multi.configMapName" . }}
        {{- if .Values.persistence.enabled }}
        - name: logs-volume
          persistentVolumeClaim:
            claimName: {{ include "nebula-auth-multi.pvcName" . }}
        {{- end }}
        {{- with .Values.volumes }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
