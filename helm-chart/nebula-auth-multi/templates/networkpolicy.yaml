{{- if .Values.networkPolicy.enabled }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "nebula-auth-multi.fullname" . }}
  labels:
    {{- include "nebula-auth-multi.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      {{- include "nebula-auth-multi.selectorLabels" . | nindent 6 }}
  policyTypes:
    - Ingress
    - Egress
  {{- if .Values.networkPolicy.ingress }}
  ingress:
    {{- toYaml .Values.networkPolicy.ingress | nindent 4 }}
  {{- else }}
  ingress:
    - from: []
      ports:
        - protocol: TCP
          port: {{ .Values.service.targetPort }}
  {{- end }}
  {{- if .Values.networkPolicy.egress }}
  egress:
    {{- toYaml .Values.networkPolicy.egress | nindent 4 }}
  {{- else }}
  egress:
    - {}
  {{- end }}
{{- end }}
