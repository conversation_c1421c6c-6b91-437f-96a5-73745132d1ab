apiVersion: v1
kind: Secret
metadata:
  name: {{ include "nebula-auth-multi.databaseSecretName" . }}
  labels:
    {{- include "nebula-auth-multi.labels" . | nindent 4 }}
type: Opaque
data:
  password: {{ .Values.database.password | b64enc }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "nebula-auth-multi.redisSecretName" . }}
  labels:
    {{- include "nebula-auth-multi.labels" . | nindent 4 }}
type: Opaque
data:
  password: {{ .Values.redis.password | default "" | b64enc }}
