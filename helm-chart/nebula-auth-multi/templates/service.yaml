apiVersion: v1
kind: Service
metadata:
  name: {{ include "nebula-auth-multi.fullname" . }}
  labels:
    {{- include "nebula-auth-multi.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: {{ .Values.service.protocol }}
      name: http
  selector:
    {{- include "nebula-auth-multi.selectorLabels" . | nindent 4 }}
