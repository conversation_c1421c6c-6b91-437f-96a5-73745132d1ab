apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "nebula-auth-multi.configMapName" . }}
  labels:
    {{- include "nebula-auth-multi.labels" . | nindent 4 }}
data:
  application.yml: |
    servicename: {{ .Values.app.name }}
    server:
      port: {{ .Values.service.targetPort }}
      servlet:
        context-path: {{ .Values.app.contextPath }}
        encoding:
          charset: UTF-8
          force: true
      config: classpath:logback-spring.xml
      error:
        include-message: ALWAYS

    springdoc:
      packagesToScan:
        - com.dcits

    spring:
      mvc:
        pathmatch:
          matching-strategy: ant_path_matcher
      application:
        name: ${servicename}
      datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        username: {{ .Values.database.username }}
        password: ${DB_PASSWORD}
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: {{ include "nebula-auth-multi.databaseUrl" . }}
        druid:
          initialSize: {{ .Values.database.druid.initialSize }}
          maxWait: {{ .Values.database.druid.maxWait }}
          timeBetweenEvictionRunsMillis: {{ .Values.database.druid.timeBetweenEvictionRunsMillis }}
          minEvictableIdleTimeMillis: {{ .Values.database.druid.minEvictableIdleTimeMillis }}
          validationQuery: {{ .Values.database.druid.validationQuery | quote }}
          testWhileIdle: {{ .Values.database.druid.testWhileIdle }}
          testOnBorrow: {{ .Values.database.druid.testOnBorrow }}
          testOnReturn: {{ .Values.database.druid.testOnReturn }}
          poolPreparedStatements: {{ .Values.database.druid.poolPreparedStatements }}
          connectionProperties: {{ .Values.database.druid.connectionProperties }}
          useGlobalDataSourceStat: {{ .Values.database.druid.useGlobalDataSourceStat }}
      {{- if .Values.nacos.enabled }}
      cloud:
        nacos:
          discovery:
            enabled: {{ .Values.nacos.discovery.enabled }}
            server-addr: {{ .Values.nacos.serverAddr }}
            group: {{ include "nebula-auth-multi.galaxyGroup" . }}
            namespace: {{ include "nebula-auth-multi.galaxyNamespace" . }}
            clusterName: {{ .Values.app.galaxy.availabilityZone }}
          config:
            enabled: {{ .Values.nacos.config.enabled }}
      {{- end }}
      redis:
        host: {{ .Values.redis.host }}
        port: {{ .Values.redis.port }}
        {{- if .Values.redis.password }}
        password: ${REDIS_PASSWORD}
        {{- end }}
        database: {{ .Values.redis.database }}
      jackson:
        default-property-inclusion: non_null
      messages:
        encoding: UTF-8
        basename: i18n/messages
      flyway:
        enabled: false
        table: nebula_auth_flyway_schema_history
        check-location: false
        batch-enabled: true
        repair: false
        force-repeat: false
        baseline-on-migrate: true
        validate-on-migrate: true
        placeholderReplacement: false
        baseline-version: ********
        locations:
          - classpath:db/migration/{vendor}

    {{- if .Values.monitoring.enabled }}
    management:
      {{- if .Values.monitoring.prometheus.enabled }}
      metrics:
        export:
          prometheus:
            enabled: true
      {{- end }}
      endpoint:
        health:
          show-details: ALWAYS
      endpoints:
        web:
          exposure:
            include: {{ .Values.monitoring.actuator.endpoints.web.exposure.include | quote }}
    {{- end }}

    pagehelper:
      pageSizeZero: true
    mybatis:
      mapperLocations: classpath:mapper/*.xml
      typeAliasesPackage: com.dcits.domain
      configuration:
        database-id: {{ .Values.database.type }}

    auth:
      locale: {{ .Values.app.auth.locale }}
      token-store: {{ .Values.app.auth.tokenStore }}
      token-timeout: {{ .Values.app.auth.tokenTimeout }}
      whitelist:
        - /sso/getSSOUrl
        - /verify/code
        - GET /user/get
        - POST /user/modifyPwd
        - POST /user/editPersonal
        - /oauth/**
        - /audit
        - GET /api/apiEntity
        - GET /menu/userNav
        - GET /menu/addition
        - GET /menu/availabilityZoneAndRegion
        - GET /menu/domainUrl
        - GET /menu/currentDomainUrl
        - POST /user/modifyInitPwd
        - GET /user/auth
        - /frame/addFlow
        - GET /platformApps
        - GET /platformAppNames
        - GET /tenant
        - GET /tenant/{tenant}/workspaces
        - GET /tenant/{tenantId}/workspaces/{workspaceId}
        - GET /region
        - GET /region/{regionId}
        - GET /region/{regionId}/availabilityZone
        - GET /region/{regionId}/availabilityZone/{availabilityZoneId}
        - /images/**
        - /*.html
        - /v2/**
        - /webjars/**
        - /favicon.ico
        - /v3/**
        - /swagger-resources/**
        - /swagger-ui/**
        - /systeminfo
        - /actuator/**
        - /settings/download
        - /menu/list
        - /api/add
        - GET /nebula-audit/platform/test
        - GET /platform/test
        - /platform/logs
        - /platform/summary
        - /tenant/{tenant}/projectApps
        - GET /deployMode
        - GET /allHosts
        - GET /tenant/{tenant}/subPlatformApps
        - GET /user/list
        - GET /singleTenant/detail

    login:
      error-times: {{ .Values.app.login.errorTimes }}
      relogin-time: {{ .Values.app.login.reloginTime }}

    logging:
      level:
        root: {{ .Values.app.logging.level.root }}
        com.dcits: {{ .Values.app.logging.level.dcits }}
      file:
        path: {{ .Values.app.logging.path }}

    galaxy:
      deployMode: {{ .Values.app.galaxy.deployMode }}
      tenantMode: {{ .Values.app.galaxy.tenantMode }}
      domain: {{ .Values.app.galaxy.domain }}
      tenant: {{ .Values.app.galaxy.tenant }}
      workspace: {{ .Values.app.galaxy.workspace }}
      region: {{ .Values.app.galaxy.region }}
      availability-zone: {{ .Values.app.galaxy.availabilityZone }}
      {{- if .Values.app.galaxy.logicUnitId }}
      logicUnitId: {{ .Values.app.galaxy.logicUnitId }}
      {{- end }}
      {{- if .Values.app.galaxy.phyUnitId }}
      phyUnitId: {{ .Values.app.galaxy.phyUnitId }}
      {{- end }}
      nebula:
        url: {{ .Values.app.galaxy.nebula.url }}
      app:
        url: {{ .Values.app.galaxy.app.url }}
      vmapp:
        url: {{ .Values.app.galaxy.vmapp.url }}
      nacos:
        url: {{ .Values.app.galaxy.nacos.url }}

    international:
      enabled: true
