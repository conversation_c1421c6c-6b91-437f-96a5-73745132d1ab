servicename: auth-service
server:
  port: 8085
  servlet:
    context-path: /oms/uaa
    encoding:
      charset: UTF-8
      force: true
  config: classpath:logback-spring.xml
  error:
    include-message: ALWAYS

springdoc:
  packagesToScan:
    - com.dcits

spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  application:
    name: ${servicename}
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initialSize: 5
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      poolPreparedStatements: true
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      # 合并多个DruidDataSource的监控数据
      useGlobalDataSourceStat: true
  jackson:
    default-property-inclusion: non_null

  messages:
    encoding: UTF-8
    basename: i18n/messages
  flyway:
    enabled: false
    table: nebula_auth_flyway_schema_history
    check-location: false
    batch-enabled: true
    repair: false
    force-repeat: false
    baseline-on-migrate: true
    validate-on-migrate: true
    placeholderReplacement: false
    baseline-version: 1.5.0.17
    locations:
      - classpath:db/migration/{vendor}

management:
  metrics:
    export:
      prometheus:
        enabled: true #是否启用将指标导出到Prometheusendpoints:wen
  endpoint:
    health:
      show-details: ALWAYS
  endpoints:
    web:
      exposure:
        include: '*' #访问路径前缀
      #base-path: /actuator

pagehelper:
  pageSizeZero: true
mybatis:
  mapperLocations: classpath:mapper/*.xml
  typeAliasesPackage: com.dcits.domain

auth:
  #国际化，zh-CN(默认：中文)，en-US(英文)
  locale: zh-CN
  token-store: redis
  token-timeout: 7200
  whitelist:
    - /sso/getSSOUrl
    - /verify/code
    - GET /user/get
    - POST /user/modifyPwd
    - POST /user/editPersonal
    - /oauth/**
    - /audit
    - GET /api/apiEntity
    - GET /menu/userNav
    - GET /menu/addition
    - GET /menu/availabilityZoneAndRegion
    - GET /menu/domainUrl
    - GET /menu/currentDomainUrl
    - POST /user/modifyInitPwd
    - GET /user/auth
    - /frame/addFlow
    - GET /platformApps
    - GET /platformAppNames
    - GET /tenant
    - GET /tenant/{tenant}/workspaces
    - GET /tenant/{tenantId}/workspaces/{workspaceId}
    - GET /region
    - GET /region/{regionId}
    - GET /region/{regionId}/availabilityZone
    - GET /region/{regionId}/availabilityZone/{availabilityZoneId}
    - /images/**
    - /*.html
    - /v2/**
    - /webjars/**
    - /favicon.ico
    - /v3/**
    - /swagger-resources/**
    - /swagger-ui/**
    - /systeminfo
    - /actuator/**
    - /settings/download
    - /menu/list
    - /api/add
    - GET /nebula-audit/platform/test
    - GET /platform/test
    - /platform/logs
    - /platform/summary
    - /tenant/{tenant}/projectApps
    - GET /deployMode
    - GET /allHosts
    - GET /tenant/{tenant}/subPlatformApps
    - GET /user/list
    - GET /singleTenant/detail

# 登录配置
login:
  error-times: 5 #登录失败次数配置（5次后锁定10分钟）
  relogin-time: 10 #锁定后重试间隔 单位分钟
logging:
  level:
    root: INFO
    com.dcits: DEBUG
  file:
    path: /apps/logs
galaxy:
  deployMode: container # 部署模式container、vm
  tenantMode: multi
  domain: nebula-xian.dcits.com
  nebula:
    url: http://nebula-core-service.nebula-system:8080
  app:
    url: http://aries-app-center-service.galaxy4:8085
  vmapp:
    url: http://aries-legacy-app-center-service.galaxy4:8087
  #availability-zone: auuql4gkwck4
  #region: default

international:
  enabled: true